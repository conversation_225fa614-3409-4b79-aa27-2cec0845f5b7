/* ========================================
   SLIDER & BANNER STYLES - FINAL VERSION
   Tái cấu trúc hoàn chỉnh cho hiệu suất và bảo trì.
   ======================================== */

/* ========================================
   1. CONFIGURATION - CSS VARIABLES
   ======================================== */
:root {
  /* -- Timings -- */
  --animation-duration-fast: 0.6s;
  --animation-duration-medium: 1.2s;
  --animation-duration-slow: 3s; /* Dùng cho Ken <PERSON> effect */

  /* -- Easing Functions -- */
  --ease-in-out: ease-in-out;
  --ease-in: ease-in;
  --ease-out: ease-out;
  --ease-cubic: cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* -- Transform Values -- */
  --scale-active-slide: 1.05;
  --scale-fade-out: 1.05;
  --scale-fade-in: 0.95;
}

/* ========================================
   2. KEYFRAME ANIMATIONS
   ======================================== */

/* Hiệu ứng cho banner title BIẾN MẤT */
@keyframes titleFadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* Hiệu ứng cho banner title HIỆN RA */
@keyframes titleFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Hiệu ứng cho banner title xuất hiện LẦN ĐẦU TIÊN */
@keyframes initialZoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ========================================
   3. HEADER SLIDER (Ảnh nền)
   ======================================== */

.header-slider {
  visibility: hidden; /* JS sẽ đổi thành 'visible' để tránh FOUC */
}

.header-slider .slide {
  overflow: hidden;
  position: relative;
}

/* Hiệu ứng Ken Burns (phóng to nhẹ) cho ảnh nền */
.header-slider .slide img {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
  transform-origin: center center;
  transform: scale(1);
  transition: transform var(--animation-duration-slow) var(--ease-in-out);
}

.header-slider .slick-active img {
  transform: scale(var(--scale-active-slide));
}

/* ========================================
   4. BANNER LAYOUT (Chỉ dùng để căn chỉnh)
   ======================================== */

/* -- Base Styles -- */
.custom-slider-banner,
.custom-slider-banner-right {
  width: 100%;
  display: grid;
}

.custom-slider-banner h2,
.custom-slider-banner-right h2 {
  padding: 0;
  margin: 0;
}

/* -- Alignment Modifiers -- */
.custom-slider-banner {
  justify-content: end;
}
.custom-slider-banner h2 {
  text-align: right;
}

.custom-slider-banner-right {
  justify-content: start;
}
.custom-slider-banner-right h2 {
  text-align: left;
}

/* ========================================
   5. BANNER TITLE IMAGE & ANIMATIONS
   ======================================== */

.banner-title-img {
  width: 50%;
  display: block !important;
  transform-origin: center center;
  /* Bắt đầu ẩn, JS sẽ điều khiển việc hiển thị qua các lớp animation */
  opacity: 0;
}

/* Lớp cho hiệu ứng zoom-in ban đầu khi tải trang */
.banner-title-img.is-visible {
  opacity: 1;
  animation: initialZoomIn var(--animation-duration-medium) var(--ease-cubic) forwards;
}

/* Lớp kích hoạt khi banner cần MỜ ĐI */
.banner-title-img.is-animating-out {
  animation: titleFadeOut 0.8s ease-in forwards;
}

/* Lớp kích hoạt khi banner cần HIỆN RA */
.banner-title-img.is-animating-in {
  opacity: 1; /* Phải có để animation có thể chạy từ opacity: 0 */
  animation: titleFadeIn 0.8s ease-out forwards;
}


/* ========================================
   6. PERFORMANCE & ACCESSIBILITY
   ======================================== */

/* Kích hoạt tăng tốc phần cứng cho các phần tử chuyển động */
.header-slider .slide img,
.banner-title-img {
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform, opacity;
  -webkit-font-smoothing: antialiased;
}

/* Tối ưu hóa render cho slide container */
.header-slider .slide {
  contain: layout style paint;
}

/* Tắt animation cho người dùng yêu cầu giảm chuyển động */
@media (prefers-reduced-motion: reduce) {
  .header-slider .slide img,
  .banner-title-img {
    animation: none !important;
    transition: none !important;
  }
}

/* ========================================
   7. RESPONSIVE DESIGN - MOBILE
   ======================================== */

@media screen and (max-width: 640px) {
  .slider-container {
    height: 18rem;
    max-height: 44rem; /* Áp dụng cho body.home */
  }

  .slick-dots {
    bottom: 26rem !important;
  }

  .banner-title-img {
    width: 100%;
  }

  .custom-slider-banner h2,
  .custom-slider-banner-right h2 {
    width: 100%;
    display: table-caption; /* Giữ lại giải pháp layout cũ cho mobile */
  }

  .custom-slider-banner-right h2 {
    text-align: left !important;
  }

  .banner-title h2:nth-child(1) {
    margin-bottom: -2.5rem;
  }
}