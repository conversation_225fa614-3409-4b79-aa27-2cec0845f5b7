/* ========================================
   SLIDER ANIMATIONS & BANNER STYLES
   Optimized CSS for header slider and banner animations
   ======================================== */

/* ========================================
   KEYFRAME ANIMATIONS
   ======================================== */

@keyframes fadeOutIn {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideFromLeft {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.85);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideFromRightToLeft {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.85);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes zoomOutThenIn {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.85);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes zoomInEffect {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoomOutEffect {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(1.1);
  }
}

/* ========================================
   BANNER COMPONENTS
   ======================================== */

.custom-slider-banner {
  width: 100%;
  display: grid;
  justify-content: end;
  animation: zoomInEffect 3s ease-out forwards;
}

.custom-slider-banner h2 {
  text-align: -webkit-right;
  animation: zoomInEffect 3s ease-out forwards;
}

.custom-slider-banner.slick-active {
  animation: zoomOutThenIn 2s ease-in-out forwards;
}

.custom-slider-banner:not(.slick-active) {
  animation: zoomInEffect 3s ease-out forwards;
}

.custom-slider-banner-right {
  width: 100%;
  display: grid;
  justify-content: start;
  animation: zoomInEffect 3s ease-out forwards;
}

.custom-slider-banner-right h2 {
  text-align: left;
  animation: zoomInEffect 3s ease-out forwards;
}

.custom-slider-banner-hidden {
  display: none;
}

.banner-title-bg.slick-active {
  animation: zoomOutThenIn 2s ease-in-out forwards;
}

.banner-title-bg:not(.slick-active) {
  animation: zoomInEffect 3s ease-out forwards;
}

/* ========================================
   BANNER TITLE IMAGE
   ======================================== */

.banner-title-img {
  width: 50%;
  transform-origin: center center;
  will-change: transform, opacity;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  display: block !important;
}

.banner-title-img.animate-slide-left {
  animation: slideFromLeft 4s ease-in-out forwards !important;
}

.banner-title-img.animate-slide-right {
  animation: slideFromRightToLeft 4s ease-in-out forwards !important;
}

.banner-title-img.animate-fade {
  animation: fadeOutIn 4s ease-in-out forwards !important;
}

/* ========================================
   HEADER SLIDER
   ======================================== */

.header-slider {
  visibility: hidden;
}

.header-slider .slide {
  overflow: hidden;
  position: relative;
}

.header-slider .slide img {
  transform: scale(1.05);
  transform-origin: center center;
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
  transition: transform 3s ease-in-out;
}

.header-slider .slide:not(.slick-active) img {
  transform: scale(1);
}

.header-slider .slick-active img {
  transform: scale(1.05);
}

/* ========================================
   RESPONSIVE DESIGN - MOBILE
   ======================================== */

@media screen and (max-width: 640px) {
  .header-slider .slide img {
    width: 100%;
    height: auto;
    display: block;
    max-width: 100%;
    object-fit: cover;
  }

  .slick-dots {
    bottom: 26rem !important;
  }

  .slider-container {
    height: 18rem;
  }

  .slick-slider .slick-track,
  .slick-slider .slick-list {
    position: relative;
    margin-bottom: 0;
  }

  .custom-slider-banner h2 {
    width: 100%;
    display: table-caption;
    justify-content: end;
  }

  .custom-slider-banner-right h2 {
    width: 100%;
    display: table-caption;
    justify-content: start;
    text-align: left !important;
  }

  .banner-title-img {
    width: 100%;
    transform-origin: center center;
    will-change: transform, opacity;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
  }

  .banner-title h2:nth-child(1) {
    margin-bottom: -2.5rem;
  }

  .site-policy .title h3 {
    color: #212121;
  }

  body.home .slider-container {
    max-height: 44rem;
  }
}

/* ========================================
   PERFORMANCE OPTIMIZATIONS
   ======================================== */

/* Enable hardware acceleration for smooth animations */
.banner-title-img,
.header-slider .slide img,
.custom-slider-banner,
.custom-slider-banner-right {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* Reduce paint operations during animations */
.header-slider .slide {
  contain: layout style paint;
}

/* Optimize for animation performance */
@media (prefers-reduced-motion: reduce) {
  .banner-title-img,
  .custom-slider-banner,
  .custom-slider-banner-right,
  .custom-slider-banner h2,
  .custom-slider-banner-right h2 {
    animation: none !important;
    transition: none !important;
  }
}
