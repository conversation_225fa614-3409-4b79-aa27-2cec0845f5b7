/**
 * Slider Controller
 * Optimized JavaScript for header slider functionality
 * Handles navigation highlighting, slide management, and banner animations
 */

(function($) {
    'use strict';

    // Configuration object for better maintainability
    const SliderConfig = {
        slideMap: {
            '/': [0, 1, 2, 3],
            '': [0, 1, 2, 3],
            '/recruit': [4],
            '/business': [7],
            '/introduce': [8],
            '/sitepolicy': [9],
            '/inquiry': [10],
            '/about': [11],
            '/privacy': [9]
        },
        bannerClassMap: {
            1: 'custom-slider-banner',
            2: 'custom-slider-banner',
            3: 'custom-slider-banner-right'
        },
        slickOptions: {
            autoplay: true,
            autoplaySpeed: 3000,
            speed: 5000,
            slidesToShow: 1,
            slidesToScroll: 1,
            arrows: false,
            dots: true,
            pauseOnHover: false,
            pauseOnFocus: false,
            fade: true
        },
        animationDelay: 100,
        selectors: {
            headerSlider: '.header-slider',
            bannerTitle: '.banner-title',
            bannerImg: '.banner-title-img',
            navLinks: 'nav ul li a'
        }
    };

    /**
     * Initialize navigation highlighting
     */
    function initNavigation() {
        const currentUrl = window.location.pathname.replace(/\/$/, '');
        
        $(SliderConfig.selectors.navLinks).each(function() {
            const href = $(this).attr('href').replace(/\/$/, '');
            if (href === currentUrl) {
                $(this).addClass('active');
            }
        });
    }

    /**
     * Filter slides based on current URL
     * @param {string} currentUrl - Current page URL
     */
    function filterSlides(currentUrl) {
        const indices = SliderConfig.slideMap.hasOwnProperty(currentUrl) 
            ? SliderConfig.slideMap[currentUrl] 
            : [0];

        $(SliderConfig.selectors.headerSlider + ' .slide').each(function(index) {
            if (!indices.includes(index)) {
                $(this).remove();
            }
        });

        return indices;
    }

    /**
     * Initialize Slick slider
     * @param {Array} indices - Array of slide indices to show
     */
    function initSlider(indices) {
        const $slider = $(SliderConfig.selectors.headerSlider);
        
        // Make slider visible
        $slider.css('visibility', 'visible');
        
        // Configure autoplay based on slide count
        const slickOptions = {
            ...SliderConfig.slickOptions,
            autoplay: indices.length > 1
        };
        
        // Initialize Slick
        $slider.slick(slickOptions);
        
        // Bind slide change event
        $slider.on('beforeChange', handleSlideChange);
    }

    /**
     * Handle slide change animations
     * @param {Event} event - Slick event
     * @param {Object} slick - Slick instance
     * @param {number} currentSlide - Current slide index
     * @param {number} nextSlide - Next slide index
     */
    function handleSlideChange(event, slick, currentSlide, nextSlide) {
        const $bannerTitle = $(SliderConfig.selectors.bannerTitle);
        const $bannerImg = $(SliderConfig.selectors.bannerImg);
        
        // Update banner classes
        updateBannerClasses($bannerTitle, nextSlide);
        
        // Handle banner image animations
        if ($bannerImg.length > 0) {
            animateBannerImage($bannerImg);
        }
    }

    /**
     * Update banner title classes
     * @param {jQuery} $bannerTitle - Banner title element
     * @param {number} nextSlide - Next slide index
     */
    function updateBannerClasses($bannerTitle, nextSlide) {
        // Remove all banner classes
        $bannerTitle.removeClass([
            'custom-slider-banner',
            'custom-slider-banner-right',
            'custom-slider-banner-hidden',
            'banner-title-bg'
        ].join(' '));

        // Add new banner class if exists
        if (SliderConfig.bannerClassMap[nextSlide]) {
            $bannerTitle.addClass(SliderConfig.bannerClassMap[nextSlide]);
        }
    }

    /**
     * Animate banner image with optimized performance
     * @param {jQuery} $bannerImg - Banner image element
     */
    function animateBannerImage($bannerImg) {
        // Reset animation state
        resetImageAnimation($bannerImg);

        // Apply animation with delay for better performance
        setTimeout(() => {
            applyImageAnimation($bannerImg);
        }, SliderConfig.animationDelay);
    }

    /**
     * Reset image animation state
     * @param {jQuery} $bannerImg - Banner image element
     */
    function resetImageAnimation($bannerImg) {
        $bannerImg.stop(true, false);
        $bannerImg.css({
            'animation': 'none',
            'transform': 'scale(1) translateX(0)',
            'opacity': '1'
        });
        
        // Force reflow
        $bannerImg[0].offsetHeight;
    }



    /**
     * Apply appropriate animation to banner image
     * @param {jQuery} $bannerImg - Banner image element
     */
    function applyImageAnimation($bannerImg) {
        // Remove existing animation classes
        $bannerImg.removeClass('animate-slide-left animate-fade animate-slide-right');

        // Apply slide-right animation for all transitions (like slide 3 to slide 0)
        $bannerImg.addClass('animate-slide-right');
    }

    /**
     * Initialize the slider system
     */
    function init() {
        // Wait for DOM to be ready
        $(document).ready(function() {
            const currentUrl = window.location.pathname.replace(/\/$/, '');
            
            // Initialize navigation
            initNavigation();
            
            // Filter slides and initialize slider
            const indices = filterSlides(currentUrl);
            initSlider(indices);
        });
    }

    // Initialize when DOM is ready
    init();

})(jQuery);
