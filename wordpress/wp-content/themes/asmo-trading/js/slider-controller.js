/**
 * Slider Controller
 * VERSION 4.0 - Synchronized & Smooth Animations
 * Utilizes beforeChange and afterChange events for perfect timing.
 */

(function($) {
    'use strict';

    // Configuration object
    const SliderConfig = {
        slideMap: {
            '/': [0, 1, 2, 3],
            '': [0, 1, 2, 3],
            '/recruit': [4],
            '/business': [7],
            '/introduce': [8],
            '/sitepolicy': [9],
            '/inquiry': [10],
            '/about': [11],
            '/privacy': [9]
        },
        bannerClassMap: {
            1: 'custom-slider-banner',
            2: 'custom-slider-banner',
            3: 'custom-slider-banner-right'
        },
        slickOptions: {
            autoplay: true,
            autoplaySpeed: 4000, // Tăng nhẹ thời gian để khớp với animation
            speed: 1200,         // Tốc độ fade của slide nền
            slidesToShow: 1,
            slidesToScroll: 1,
            arrows: false,
            dots: true,
            pauseOnHover: false,
            pauseOnFocus: false,
            fade: true
        },
        selectors: {
            headerSlider: '.header-slider',
            bannerTitle: '.banner-title',
            bannerImg: '.banner-title-img',
        }
    };

    /**
     * Handles the animation for the banner title LEAVING the screen.
     */
    function onBeforeChange(event, slick, currentSlide, nextSlide) {
        const $bannerImg = $(SliderConfig.selectors.bannerImg);
        if ($bannerImg.length > 0) {
            $bannerImg.removeClass('is-visible is-animating-in').addClass('is-animating-out');
        }
    }

    /**
     * Handles the animation for the banner title ENTERING the screen.
     * Also updates layout classes for the new slide.
     */
    function onAfterChange(event, slick, currentSlide) {
        const $bannerTitle = $(SliderConfig.selectors.bannerTitle);
        const $bannerImg = $(SliderConfig.selectors.bannerImg);

        // Cập nhật lớp layout cho banner (phải/trái)
        updateBannerLayout($bannerTitle, currentSlide);

        if ($bannerImg.length > 0) {
            // Đợi fade-out hoàn thành (600ms) trước khi bắt đầu fade-in
            setTimeout(function() {
                $bannerImg.removeClass('is-animating-out').addClass('is-animating-in');
            }, 650); // 600ms (fade-out) + 50ms buffer
        }
    }

    /**
     * Updates banner layout classes (left/right alignment)
     */
    function updateBannerLayout($bannerTitle, currentSlide) {
        $bannerTitle.removeClass('custom-slider-banner custom-slider-banner-right');
        if (SliderConfig.bannerClassMap[currentSlide]) {
            $bannerTitle.addClass(SliderConfig.bannerClassMap[currentSlide]);
        }
    }

    /**
     * Filters slides based on the current page URL.
     */
    function filterSlides(currentUrl) {
        const indices = SliderConfig.slideMap.hasOwnProperty(currentUrl) 
            ? SliderConfig.slideMap[currentUrl] 
            : [0];
        $(SliderConfig.selectors.headerSlider + ' .slide').each(function(index) {
            if (!indices.includes(index)) {
                $(this).remove();
            }
        });
        return indices;
    }
    
    /**
     * Initializes the slider and its events.
     */
    function initSlider(indices) {
        const $slider = $(SliderConfig.selectors.headerSlider);
        const $bannerImg = $(SliderConfig.selectors.bannerImg);

        $slider.css('visibility', 'visible');

        const slickOptions = {
            ...SliderConfig.slickOptions,
            autoplay: indices.length > 1
        };

        // Gán các sự kiện ĐỒNG BỘ MỚI
        $slider.on('beforeChange', onBeforeChange);
        $slider.on('afterChange', onAfterChange);

        $slider.slick(slickOptions);
        
        // Xử lý animation ban đầu khi tải trang
        if ($bannerImg.length > 0) {
            // Cập nhật layout cho slide đầu tiên
            updateBannerLayout($(SliderConfig.selectors.bannerTitle), 0);
            // Thêm lớp để banner hiện ra lần đầu
            $bannerImg.addClass('is-visible');
        }
    }

    /**
     * Main initialization function.
     */
    function init() {
        $(document).ready(function() {
            const currentUrl = window.location.pathname.replace(/\/$/, '') || '/';
            const indices = filterSlides(currentUrl);
            initSlider(indices);
        });
    }

    init();

})(jQuery);